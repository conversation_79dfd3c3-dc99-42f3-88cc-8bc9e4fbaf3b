import React from 'react';
import { Sprout, Truck, Store, Users, ArrowRight } from 'lucide-react';

const SupplyChainSection = () => {

  const supplyChainSteps = [
    {
      id: 1,
      title: "Farmers",
      subtitle: "Fresh Harvest",
      description: "Direct sourcing from verified farmers ensuring quality and fair pricing",
      icon: <Sprout className="w-8 h-8" />,
      color: "from-green-500 to-emerald-600",
      bgColor: "bg-green-50",
      position: "top-left"
    },
    {
      id: 2,
      title: "Processing",
      subtitle: "Quality Control",
      description: "Advanced sorting, packaging, and quality assurance processes",
      icon: <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
        <div className="w-4 h-4 bg-white rounded-sm"></div>
      </div>,
      color: "from-blue-500 to-indigo-600",
      bgColor: "bg-blue-50",
      position: "top-right"
    },
    {
      id: 3,
      title: "Distribution",
      subtitle: "Smart Logistics",
      description: "Efficient distribution network with real-time tracking and optimization",
      icon: <Truck className="w-8 h-8" />,
      color: "from-orange-500 to-red-600",
      bgColor: "bg-orange-50",
      position: "bottom-left"
    },
    {
      id: 4,
      title: "Partners",
      subtitle: "Retail Network",
      description: "Seamless delivery to retail partners and end consumers",
      icon: <Store className="w-8 h-8" />,
      color: "from-purple-500 to-pink-600",
      bgColor: "bg-purple-50",
      position: "bottom-right"
    }
  ];



  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-primary-50/30 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 border-2 border-primary-300 rounded-full"></div>
        <div className="absolute bottom-20 right-20 w-24 h-24 border-2 border-secondary-300 rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-accent-300 rounded-full"></div>
      </div>

      <div className="container mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <Truck className="w-4 h-4" />
            Supply Chain Excellence
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-black text-gray-900 leading-none mb-6">
            Efficient Network
            <br />
            <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
              Channels
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience our revolutionary supply chain that connects farmers directly to consumers through 
            technology-driven logistics and partner networks.
          </p>
        </div>

        {/* First Row: Video Left + Farmers & Processing Cards Right */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">

          {/* First Video Section - Supply Chain */}
          <div className="relative">
            <div className="relative bg-gradient-to-br from-primary-400 to-secondary-500 rounded-3xl p-4 md:p-8 overflow-hidden">
              {/* Video Container */}
              <div className="relative bg-black/10 backdrop-blur-sm rounded-2xl overflow-hidden aspect-video">
                <video
                  className="w-full h-full object-cover"
                  autoPlay
                  muted
                  loop
                  playsInline
                >
                  <source src="/foodhub-gif.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>

              {/* Floating Stats */}
              <div className="absolute top-2 right-2 md:top-4 md:right-4 bg-white/90 backdrop-blur-sm rounded-lg md:rounded-xl p-2 md:p-3 shadow-lg">
                <div className="text-center">
                  <div className="text-lg md:text-2xl font-bold text-primary-600">24/7</div>
                  <div className="text-xs text-gray-600">Operations</div>
                </div>
              </div>

              <div className="absolute bottom-2 left-2 md:bottom-4 md:left-4 bg-white/90 backdrop-blur-sm rounded-lg md:rounded-xl p-2 md:p-3 shadow-lg">
                <div className="text-center">
                  <div className="text-lg md:text-2xl font-bold text-secondary-600">100%</div>
                  <div className="text-xs text-gray-600">Fresh Delivery</div>
                </div>
              </div>
            </div>
          </div>

          {/* First Two Cards: Farmers & Processing */}
          <div className="space-y-6 md:space-y-6">
            {/* Mobile: Two cards side by side */}
            <div className="grid grid-cols-2 gap-4 md:hidden">
              {supplyChainSteps.slice(0, 2).map((step) => (
                <div
                  key={step.id}
                  className={`${step.bgColor} rounded-xl p-4 hover:shadow-lg transition-all duration-300 group cursor-pointer`}
                >
                  <div className="flex flex-col items-center text-center gap-3">
                    <div className={`w-12 h-12 bg-gradient-to-br ${step.color} rounded-xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                      {React.cloneElement(step.icon, { className: "w-6 h-6" })}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-bold text-gray-800 mb-1">{step.title}</h3>
                      <p className="text-primary-600 font-semibold text-xs mb-1">{step.subtitle}</p>
                      <p className="text-gray-600 text-xs leading-relaxed">{step.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop: Stacked cards */}
            <div className="hidden md:block space-y-6">
              {supplyChainSteps.slice(0, 2).map((step) => (
                <div
                  key={step.id}
                  className={`${step.bgColor} rounded-2xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer`}
                >
                  <div className="flex items-start gap-4">
                    <div className={`w-16 h-16 bg-gradient-to-br ${step.color} rounded-2xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                      {step.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-xl font-bold text-gray-800">{step.title}</h3>
                        <span className="text-sm font-semibold text-gray-500">#{step.id}</span>
                      </div>
                      <p className="text-primary-600 font-semibold mb-2">{step.subtitle}</p>
                      <p className="text-gray-600 leading-relaxed">{step.description}</p>
                    </div>
                    <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Second Row: Distribution & Partners Cards Left + Video Right */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">

          {/* Second Two Cards: Distribution & Partners */}
          <div className="space-y-6 lg:order-1">
            {/* Mobile: Two cards side by side */}
            <div className="grid grid-cols-2 gap-4 md:hidden">
              {supplyChainSteps.slice(2, 4).map((step) => (
                <div
                  key={step.id}
                  className={`${step.bgColor} rounded-xl p-4 hover:shadow-lg transition-all duration-300 group cursor-pointer`}
                >
                  <div className="flex flex-col items-center text-center gap-3">
                    <div className={`w-12 h-12 bg-gradient-to-br ${step.color} rounded-xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                      {React.cloneElement(step.icon, { className: "w-6 h-6" })}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-bold text-gray-800 mb-1">{step.title}</h3>
                      <p className="text-primary-600 font-semibold text-xs mb-1">{step.subtitle}</p>
                      <p className="text-gray-600 text-xs leading-relaxed">{step.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop: Stacked cards */}
            <div className="hidden md:block space-y-6">
              {supplyChainSteps.slice(2, 4).map((step) => (
                <div
                  key={step.id}
                  className={`${step.bgColor} rounded-2xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer`}
                >
                  <div className="flex items-start gap-4">
                    <div className={`w-16 h-16 bg-gradient-to-br ${step.color} rounded-2xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                      {step.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-xl font-bold text-gray-800">{step.title}</h3>
                        <span className="text-sm font-semibold text-gray-500">#{step.id}</span>
                      </div>
                      <p className="text-primary-600 font-semibold mb-2">{step.subtitle}</p>
                      <p className="text-gray-600 leading-relaxed">{step.description}</p>
                    </div>
                    <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Second Video Section - Mobile App Integration */}
          <div className="relative lg:order-2">
            <div className="relative bg-gradient-to-br from-secondary-400 to-accent-500 rounded-3xl p-4 md:p-8 overflow-hidden">
              {/* Video Container */}
              <div className="relative bg-black/10 backdrop-blur-sm rounded-2xl overflow-hidden aspect-video">
                <video
                  className="w-full h-full object-cover"
                  autoPlay
                  muted
                  loop
                  playsInline
                >
                  <source src="/visual-fh.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>

              {/* Floating Stats for Second Video */}
              <div className="absolute top-2 right-2 md:top-4 md:right-4 bg-white/90 backdrop-blur-sm rounded-lg md:rounded-xl p-2 md:p-3 shadow-lg">
                <div className="text-center">
                  <div className="text-lg md:text-2xl font-bold text-secondary-600">Robust</div>
                  <div className="text-xs text-gray-600">Mobile App
                    Integration
                  </div>
                </div>
              </div>

              <div className="absolute bottom-2 left-2 md:bottom-4 md:left-4 bg-white/90 backdrop-blur-sm rounded-lg md:rounded-xl p-2 md:p-3 shadow-lg">
                <div className="text-center">
                  <div className="text-lg md:text-2xl font-bold text-accent-600">Real-time</div>
                  <div className="text-xs text-gray-600">Tracking</div>
                </div>
              </div>

              <div className="absolute top-1/2 left-2 md:left-4 bg-white/90 backdrop-blur-sm rounded-lg md:rounded-xl p-2 md:p-3 shadow-lg">
                <div className="text-center">
                  <div className="text-lg md:text-2xl font-bold text-purple-600">AI</div>
                  <div className="text-xs text-gray-600">Powered</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Partner Network Visualization */}
        <div className="bg-white rounded-2xl md:rounded-3xl p-4 md:p-8 lg:p-12 shadow-xl">
          <div className="text-center mb-6 md:mb-12">
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-2 md:mb-4">Our Distribution Network</h3>
            <p className="text-gray-600 text-sm md:text-lg">Connecting farmers to consumers through strategic partnerships</p>
          </div>

          {/* Network Diagram - Redesigned */}
          <div className="relative max-w-5xl mx-auto">
            {/* Absolute positioned layout for perfect centering */}
            <div className="relative min-h-[300px] md:min-h-[400px] lg:min-h-[500px] flex items-center justify-center">

              {/* Central Hub - FoodHub - Perfectly Centered */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <div className="flex flex-col items-center">
                  <div className="relative">
                    <div className="w-20 h-20 md:w-28 md:h-28 lg:w-36 lg:h-36 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl md:rounded-3xl flex items-center justify-center shadow-xl md:shadow-2xl">
                      <div className="w-14 h-14 md:w-20 md:h-20 lg:w-24 lg:h-24 bg-white rounded-xl md:rounded-2xl flex items-center justify-center">
                        <div className="text-center">
                          <Truck className="w-6 h-6 md:w-8 md:h-8 lg:w-10 lg:h-10 text-primary-600 mx-auto mb-1" />
                          <div className="text-xs md:text-sm font-bold text-primary-600">Hub</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-center mt-2 md:mt-4 font-bold text-gray-800 text-sm md:text-lg lg:text-xl">FoodHub</p>
                  <p className="text-center text-xs md:text-sm text-gray-500">Central Distribution</p>
                </div>
              </div>

              {/* Corner Nodes - Positioned around the center */}

              {/* Top Left - Logistics */}
              <div className="absolute top-4 left-4 md:top-6 md:left-6 lg:top-8 lg:left-8">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 md:w-16 md:h-16 lg:w-20 lg:h-20 bg-orange-100 rounded-xl md:rounded-2xl flex items-center justify-center shadow-md md:shadow-lg hover:shadow-xl transition-all duration-300 group">
                    <Truck className="w-6 h-6 md:w-8 md:h-8 lg:w-10 lg:h-10 text-orange-600 group-hover:scale-110 transition-transform" />
                  </div>
                  <p className="text-center mt-1 md:mt-2 lg:mt-3 font-semibold text-gray-800 text-xs md:text-sm lg:text-base">Logistics</p>
                  <p className="text-center text-xs md:text-sm text-gray-500 hidden md:block">Smart Distribution</p>
                </div>
              </div>

              {/* Top Right - Consumers */}
              <div className="absolute top-4 right-4 md:top-6 md:right-6 lg:top-8 lg:right-8">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 md:w-16 md:h-16 lg:w-20 lg:h-20 bg-purple-100 rounded-xl md:rounded-2xl flex items-center justify-center shadow-md md:shadow-lg hover:shadow-xl transition-all duration-300 group">
                    <Users className="w-6 h-6 md:w-8 md:h-8 lg:w-10 lg:h-10 text-purple-600 group-hover:scale-110 transition-transform" />
                  </div>
                  <p className="text-center mt-1 md:mt-2 lg:mt-3 font-semibold text-gray-800 text-xs md:text-sm lg:text-base">Consumers</p>
                  <p className="text-center text-xs md:text-sm text-gray-500 hidden md:block">End Customers</p>
                </div>
              </div>

              {/* Bottom Left - Farmers */}
              <div className="absolute bottom-12 left-4 md:bottom-16 md:left-6 lg:bottom-20 lg:left-8">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 md:w-16 md:h-16 lg:w-20 lg:h-20 bg-green-100 rounded-xl md:rounded-2xl flex items-center justify-center shadow-md md:shadow-lg hover:shadow-xl transition-all duration-300 group">
                    <Sprout className="w-6 h-6 md:w-8 md:h-8 lg:w-10 lg:h-10 text-green-600 group-hover:scale-110 transition-transform" />
                  </div>
                  <p className="text-center mt-1 md:mt-2 lg:mt-3 font-semibold text-gray-800 text-xs md:text-sm lg:text-base">Farmers</p>
                  <p className="text-center text-xs md:text-sm text-gray-500 hidden md:block">Fresh Produce</p>
                </div>
              </div>

              {/* Bottom Right - Retailers */}
              <div className="absolute bottom-12 right-4 md:bottom-16 md:right-6 lg:bottom-20 lg:right-8">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 md:w-16 md:h-16 lg:w-20 lg:h-20 bg-blue-100 rounded-xl md:rounded-2xl flex items-center justify-center shadow-md md:shadow-lg hover:shadow-xl transition-all duration-300 group">
                    <Store className="w-6 h-6 md:w-8 md:h-8 lg:w-10 lg:h-10 text-blue-600 group-hover:scale-110 transition-transform" />
                  </div>
                  <p className="text-center mt-1 md:mt-2 lg:mt-3 font-semibold text-gray-800 text-xs md:text-sm lg:text-base">Retailers</p>
                  <p className="text-center text-xs md:text-sm text-gray-500 hidden md:block">Partner Stores</p>
                </div>
              </div>

              {/* Bottom Center - Real-time Sync */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <div className="flex flex-col items-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                    <div className="w-8 h-8 border-2 border-dashed border-gray-400 rounded-full animate-spin"></div>
                  </div>
                  <p className="text-center mt-6 text-sm text-gray-500">Real-time Sync</p>
                </div>
              </div>
            </div>

            {/* Connection Lines - SVG Overlay */}
            <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
              <defs>
                <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#10B981" stopOpacity="0.4" />
                  <stop offset="50%" stopColor="#3B82F6" stopOpacity="0.6" />
                  <stop offset="100%" stopColor="#8B5CF6" stopOpacity="0.4" />
                </linearGradient>
              </defs>

              {/* Connecting lines from center to all corner nodes */}
              {/* Center to Top Left (Logistics) */}
              <line x1="50%" y1="50%" x2="20%" y2="20%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />

              {/* Center to Top Right (Consumers) */}
              <line x1="50%" y1="50%" x2="80%" y2="20%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />

              {/* Center to Bottom Left (Farmers) */}
              <line x1="50%" y1="50%" x2="20%" y2="80%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />

              {/* Center to Bottom Right (Retailers) */}
              <line x1="50%" y1="50%" x2="80%" y2="80%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />

              {/* Center to Bottom Center (Real-time Sync) */}
              <line x1="50%" y1="50%" x2="50%" y2="90%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />
            </svg>
          </div>

          {/* Network Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 pt-8 border-t border-gray-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">500+</div>
              <div className="text-sm text-gray-600">Active Farmers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary-600">50+</div>
              <div className="text-sm text-gray-600">Partner Stores</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">24/7</div>
              <div className="text-sm text-gray-600">Operations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">10k+</div>
              <div className="text-sm text-gray-600">Happy Customers</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SupplyChainSection;
